import pandas as pd  # 导入Pandas库，用于数据处理和Excel文件读写，通常简写为pd
import numpy as np  # 导入NumPy库，用于进行数值计算，特别是数组操作，通常简写为np
import matplotlib.pyplot as plt  # 导入Matplotlib的pyplot模块，用于数据可视化（绘图），通常简写为plt
import os  # 导入os模块，用于与操作系统交互

os.environ["OMP_NUM_THREADS"] = "1"
# 设置环境变量OMP_NUM_THREADS为1，这通常在导入scikit-learn之前进行。
# 它的目的是解决在某些情况下（特别是在KMeans等算法中）由于OpenMP多线程引起的潜在问题或警告，
# 例如内存泄漏或结果不可复现。将其设置为1可以强制使用单线程。

# 数据处理模块
from sklearn.preprocessing import StandardScaler  # 从scikit-learn库中导入StandardScaler，用于特征标准化
from sklearn.decomposition import PCA  # 从scikit-learn库中导入PCA (Principal Component Analysis)，用于主成分分析（降维）
from sklearn.cluster import KMeans  # 从scikit-learn库中导入KMeans，用于进行K-均值聚类分析
from sklearn.metrics import calinski_harabasz_score
from sklearn.metrics import silhouette_score
from sklearn.metrics import davies_bouldin_score
import seaborn as sns

# --- Matplotlib 全局设置 ---
# 设置matplotlib支持中文显示，防止中文标签显示为方块
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定一个支持中文的字体，如'SimHei' (黑体) 或 'Microsoft YaHei' (微软雅黑)
plt.rcParams['axes.unicode_minus'] = False  # 解决坐标轴负号'-'在某些情况下显示为方块的问题

# --- 特征名称定义 ---
# 根据题目中的表1定义13个特征的名称，方便后续在DataFrame和图表中使用
feature_names = [
    "V_a (平均速度)", "V_f (平均行驶速度)", "V_max (最大速度)",
    "a_vp (平均加速度)", "a_vd (平均减速度)", "V_std (速度标准差)",
    "a_vpstd (加速度标准差)", "a_vdstd (减速度标准差)", "T (运动学片段时长)",
    "T_i (怠速时间)", "T_a (加速时间)", "T_d (减速时间)", "T_c (匀速时间)"
]

print("--- 任务开始 ---")  # 打印任务开始的提示信息

# --- 1. 数据预处理 ---
print("\n--- 1. 数据预处理 ---")  # 打印数据预处理阶段的标题
# (1) 将 Excel 数据文件导入 python,赋值为数组 X;
try:
    # 首先检查目标Excel文件 "Data2.xlsx" 是否存在于当前工作目录
    if os.path.exists("Data2.xlsx"):
        df = pd.read_excel("Data2.xlsx", header=0)
        X = df.values  # 将读取到的Pandas DataFrame转换为NumPy数组，赋值给X
        print(f"(1) Excel数据已导入，X的形状: {X.shape}")  # 打印成功信息和X的形状 (行数, 列数)
    else:  # 如果文件不存在
        print("(1) 错误: 'Data2.xlsx' 文件未找到。请确保文件在当前工作目录。")

except FileNotFoundError:  # 捕获文件未找到的错误 (os.path.exists应能避免此，但保留以防万一)
    print("(1) 错误: 未找到 'Data2.xlsx' 文件。")

# (2) 编程、以表格形式打印前20个"运动学片段"样本数据;
print("\n(2) 前20个运动学片段样本数据:")  # 打印任务2的标题
df_preview = pd.DataFrame(X[:20, :], columns=feature_names)
# print(df_preview) # 这样打印出来的不是一串不便于查看
print(df_preview.to_string())  # 使用.to_string()方法打印整个DataFrame，确保所有列都被显示

# (3) 将样本数据 X 标准化为Xs(均值为0,标准差为1);
scaler = StandardScaler()  # 创建StandardScaler对象
Xs = scaler.fit_transform(X)  # 对数据X进行拟合（计算均值和标准差）并转换（标准化处理）
print("\n(3) 样本数据已标准化为 Xs。")  # 打印标准化完成的提示
print(f"Xs 的均值 (应接近0): {np.mean(Xs, axis=0)}")  # 打印标准化后数据各列的均值，应非常接近0
print(f"Xs 的标准差 (应接近1): {np.std(Xs, axis=0)}")  # 打印标准化后数据各列的标准差，应非常接近1

# --- 2. 主成分分析 (PCA) ---
print("\n--- 2. 主成分分析 (PCA) ---")  # 打印PCA阶段的标题
# (1) 调用 sklearn 主成分分析接口函数,对样本数据 Xs 进行主成分分析,要求累积贡献率大于85%;
# n_components=0.85 表示希望保留的主成分能够解释至少85%的原始数据方差
pca = PCA(n_components=0.85)
pca.fit(Xs)  # 对标准化后的数据Xs进行PCA拟合
print(f"(1) PCA已执行，要求累积贡献率 > 85%。")  # 打印PCA执行完成的提示
print(f"   保留的主成分数量: {pca.n_components_}")  # 打印实际保留的主成分数量

# (2) 打印保留的各阶主成分和对应的贡献率;
print("\n(2) 保留的各阶主成分对应的贡献率:")  # 打印任务2.2的标题
explained_variance_ratios = pca.explained_variance_ratio_  # 获取每个保留主成分的方差解释比例（贡献率）explained_variance_ratio_是pca对象的属性
for i, ratio in enumerate(explained_variance_ratios):  # 遍历每个主成分的贡献率
    print(f"   主成分 {i + 1}: 贡献率 = {ratio:.4f}")  # 打印主成分序号和其贡献率（保留4位 小数）
print(f"   累积贡献率: {np.sum(explained_variance_ratios):.4f}")  # 打印所有保留主成分的累积贡献率

# (3) 分析所保留的主成分、所代表的样本数据特征;
print("\n(3) 主成分分析 (各主成分由原始特征的线性组合构成):")  # 打印任务2.3的标题
print("   主成分系数 (pca.components_):")  # pca.components_ 存储了主成分向量
# 将主成分系数（每个主成分由原始特征线性组合的权重）转换为DataFrame，方便查看
df_pca_components = pd.DataFrame(pca.components_, columns=feature_names)  # colums是表头
print(df_pca_components.to_string())  # 打印主成分系数矩阵
print("   题注: 上表中每一行代表一个主成分，列代表原始特征。")
print("   数值的绝对值越大，表示该原始特征对该主成分的贡献越大。")

print("\n   绘制主成分负载热力图:")
plt.figure(figsize=(12, max(4, pca.n_components_ * 0.8)))  # 动态调整高度，确保不拥挤
# 宽度固定为12，高度至少为4
# 并根据主成分数量适当增加 (每个成分大约0.8英寸高)
sns.heatmap(df_pca_components, annot=True, cmap="viridis", fmt=".2f", linewidths=.5,
            yticklabels=[f"PC{i + 1}" for i in range(pca.n_components_)])  # 设置y轴标签为主成分PC1, PC2...
plt.title('主成分负载热力图 (Principal Component Loadings)')
plt.xlabel('原始特征 (Original Features)')
plt.ylabel('主成分 (Principal Components)')
plt.xticks(rotation=45, ha="right")  # 旋转x轴标签以便阅读
plt.yticks(rotation=0)  # 确保y轴标签水平
plt.tight_layout()  # 自动调整布局，防止标签重叠
plt.show()

# (4) 绘制各阶主成分贡献率的散点图;
print("\n(4) 绘制各阶主成分贡献率的图表:")  # 打印任务2.4的标题
plt.figure(figsize=(10, 6))  # 创建一个新的图形，并设置其大小

# 绘制单个主成分贡献率 (使用散点图)
# x轴是主成分序号 (从1开始)，y轴是对应的贡献率
# s=100 设置散点的大小，alpha=0.7 设置透明度
plt.scatter(range(1, pca.n_components_ + 1), explained_variance_ratios, color='blue', s=100, alpha=0.7,
            label='单个主成分贡献率')

# 绘制累积贡献率 (使用折线图)
# x轴是主成分序号，y轴是累积贡献率
# marker='o' 在每个数据点处显示一个圆圈标记，linestyle='-' 使用实线连接各点
plt.plot(range(1, pca.n_components_ + 1), np.cumsum(explained_variance_ratios), marker='o', linestyle='-', color='red',
         label='累积贡献率')
plt.ylabel('贡献率')  # 设置y轴标签
plt.xlabel('主成分序号')  # 设置x轴标签
plt.title('主成分贡献率分析')  # 设置图表标题
plt.legend(loc='best')  # 显示图例，并自动选择最佳位置
plt.grid(True, linestyle='--', alpha=0.7)  # 显示网格线，使用虚线，并设置透明度
plt.show()  # 显示图表

# (5) 将运动学片断的样本数据Xs,转换成降维后的数据 Xd;
Xd = pca.transform(Xs)  # 使用拟合好的PCA模型将标准化数据Xs转换（投影）到新的主成分空间，得到降维后的数据Xd
print("\n(5) 样本数据 Xs 已转换为降维后的数据 Xd。")  # 打印转换完成的提示
print(f"   Xd 的形状: {Xd.shape}")  # 打印降维后数据Xd的形状，列数应等于保留的主成分数量

# 绘制Biplot分析图
print("\n   绘制 Biplot (PC1 vs PC2):")
plt.figure(figsize=(12, 10))
# 绘制样本点
plt.scatter(Xd[:, 0], Xd[:, 1], s=10, alpha=0.5, label='样本点')
# 绘制原始特征向量 (scaled for visibility)
scale_factor = 1.0 * np.max(np.abs(Xd[:, :2])) / np.max(np.abs(pca.components_[:2, :]))  # 动态缩放因子
for i, feature in enumerate(feature_names):
    plt.arrow(0, 0, pca.components_[0, i] * scale_factor, pca.components_[1, i] * scale_factor,
              color='r', alpha=0.9, head_width=0.05 * scale_factor * 0.1)  # 调整head_width
    plt.text(pca.components_[0, i] * scale_factor * 1.15, pca.components_[1, i] * scale_factor * 1.15,
             feature, color='g', ha='center', va='center', fontsize=9)
plt.xlabel(f'主成分 1 (贡献率: {pca.explained_variance_ratio_[0]:.2%})')
plt.ylabel(f'主成分 2 (贡献率: {pca.explained_variance_ratio_[1]:.2%})')
plt.title('Biplot - 主成分与原始特征')
plt.grid(True, linestyle='--', alpha=0.7)
plt.axhline(0, color='grey', lw=1)
plt.axvline(0, color='grey', lw=1)
plt.legend()
plt.tight_layout()
plt.show()

# --- 3. k均值聚类分析 ---
print("\n--- 3. k均值聚类分析 ---")  # 打印K-均值聚类阶段的标题
print("\n--- 评估不同簇数的Calinski-Harabasz分数 ---")
# 初始化
ch_scores = []
silhouette_scores = []
db_scores = []

k_range = range(2, 10)  # 评估的簇数范围可以根据需要调整，例如 range(2, 11)

for k_eval in k_range:  # 使用 k_eval 避免与后续的 k 混淆
    try:
        kmeans_eval = KMeans(n_clusters=k_eval, random_state=42, n_init='auto')  # 因为我们了解到新版的scikit-learn支持自动选取n-init.
    except TypeError:
        kmeans_eval = KMeans(n_clusters=k_eval, random_state=42, n_init=10)  # 兼容旧版sklearn，手动设置n_init=10
        # n_clusters：你想要多少个类别。
        # random_state：为了让结果可重复，固定随机过程。
        # n_init：为了找到更好的聚类结果，多次尝试不同的随机开始点，然后选最好的那次。

    kmeans_eval.fit(Xd)
    labels_eval = kmeans_eval.labels_
    ch = calinski_harabasz_score(Xd, labels_eval)
    ch_scores.append(ch)
    print(f"簇数 = {k_eval}, Calinski-Harabasz 分数 = {ch:.2f}")

    s_score = silhouette_score(Xd, labels_eval)  # 使用降维后的数据 Xd 和聚类标签 labels
    silhouette_scores.append(s_score)  # 存储轮廓系数
    print(f"    最终聚类的 Silhouette Score: {s_score:.4f}")

    db_score = davies_bouldin_score(Xd, labels_eval)
    db_scores.append(db_score)  # 存储Davies-Bouldin指数
    print(f"    最终聚类的 Davies-Bouldin Score: {db_score:.4f}")

# 找到最佳簇数
# best_k_index = np.argmax(ch_scores)
best_k = 4  # 确保从k_range正确索引
print(f"\n>>> Calinski-Harabasz 分数最高，最佳簇数为: {best_k}")
print(f"    使用预设的最佳簇数为: {best_k}")

# 可视化不同簇数下的聚类评估指标
fig, ax1 = plt.subplots(figsize=(12, 7))  # 创建一个图和第一个y轴

# 绘制 Calinski-Harabasz 分数
color_ch = 'tab:red'
ax1.set_xlabel('簇数量 (k)', fontsize=12)
ax1.set_ylabel('Calinski-Harabasz 分数', color=color_ch, fontsize=12)
ax1.plot(list(k_range), ch_scores, marker='o', linestyle='-', color=color_ch, label='CH 分数')
ax1.tick_params(axis='y', labelcolor=color_ch)
ax1.grid(True, linestyle='--', alpha=0.7, axis='y')  # 只显示y轴的网格线

# 创建第二个y轴，共享x轴，用于轮廓系数
ax2 = ax1.twinx()
color_silhouette = 'tab:blue'
ax2.set_ylabel('轮廓系数 (Silhouette Score)', color=color_silhouette, fontsize=12)
ax2.plot(list(k_range), silhouette_scores, marker='s', linestyle='--', color=color_silhouette, label='轮廓系数')
ax2.tick_params(axis='y', labelcolor=color_silhouette)

# 创建第三个y轴，共享x轴，用于Davies-Bouldin指数
# 为了避免y轴标签重叠，我们需要调整第三个y轴的位置
ax3 = ax1.twinx()
# 将第三个y轴的spine向右偏移
ax3.spines["right"].set_position(("outward", 60))  # 调整偏移量 "60"
color_db = 'tab:green'
ax3.set_ylabel('Davies-Bouldin 指数', color=color_db, fontsize=12)
ax3.plot(list(k_range), db_scores, marker='^', linestyle=':', color=color_db, label='DB 指数')
ax3.tick_params(axis='y', labelcolor=color_db)

plt.title('不同簇数下的聚类评估指标对比', fontsize=14)
ax1.set_xticks(list(k_range))  # x轴刻度

# 添加图例
# 为了在一个图例中显示所有曲线，我们需要从每个axes获取handles和labels
lines, labels_ax1 = ax1.get_legend_handles_labels()
lines2, labels_ax2 = ax2.get_legend_handles_labels()
lines3, labels_ax3 = ax3.get_legend_handles_labels()
ax1.legend(lines + lines2 + lines3, labels_ax1 + labels_ax2 + labels_ax3, loc='upper center',
           bbox_to_anchor=(0.5, -0.15), fancybox=True, shadow=True, ncol=3)

# 添加预设最佳K的垂直线
ax1.axvline(x=best_k, color='grey', linestyle='-.', linewidth=1.5, label=f'预设最佳 k = {best_k}')
# 更新图例以包含垂直线 (如果直接在ax1上画，它会自动加入ax1的图例)
lines, labels_ax1 = ax1.get_legend_handles_labels()  # 重新获取ax1的图例项
ax1.legend(lines + lines2 + lines3, labels_ax1 + labels_ax2 + labels_ax3, loc='upper center',
           bbox_to_anchor=(0.5, -0.15), fancybox=True, shadow=True, ncol=3)

fig.tight_layout()  # 调整布局以防止标签重叠
plt.subplots_adjust(bottom=0.25)  # 为图例留出更多底部空间
plt.show()
# (1) 进行 k-means 聚类分析;
# 使用 Calinski-Harabasz 评估得到的 best_k
n_clusters = best_k
print(f"\n(1) k-Means聚类将使用 {n_clusters} 个簇进行。")

# 为了处理不同sklearn版本KMeans的n_init参数差异（旧版只接受整数，新版可以是'auto'）
try:
    # 尝试使用 n_init='auto'，这是较新版本的推荐设置，它会根据数据自动选择运行次数。
    # random_state=42 确保KMeans的初始中心点选择是可复现的，便于调试和比较结果。
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init='auto')
except TypeError:  # 如果因为n_init='auto'导致TypeError（通常发生在旧版本sklearn）
    # 回退到使用一个固定的整数作为n_init的值，例如10。
    # n_init=10 表示KMeans算法会用10组不同的初始中心点运行，并选择结果最好的那一组。
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)

kmeans.fit(Xd)  # 对降维后的数据Xd进行K-均值聚类拟合
print(f"   k-Means聚类已执行完毕，实际簇数量为: {kmeans.n_clusters}")  # 打印聚类完成的提示

# 从已拟合的 kmeans_eval 模型中获取每个数据点所属的簇标签。
# kmeans_eval.labels_ 是一个一维数组，其长度与 Xd 的行数（样本数）相同。
# 数组中的每个元素代表 Xd 中对应样本被分配到的簇的编号 (从 0 到 k_eval-1)。
# 将这些标签存储在变量 labels_eval 中，以便后续用于评估。
labels = kmeans.labels_  # 获取每个样本被分配到的簇标签 (0到n_clusters-1)

ch_score = calinski_harabasz_score(Xd, labels)
print(f"    最终聚类的 Calinski-Harabasz (CH) 分数: {ch_score:.4f}")

# (2) 计算每个样本对应的分类;
print("\n(2) 每个样本对应的分类 (前200个):")
print(labels[:200])

# (3) 给出各簇的聚类中心;
cluster_centers = kmeans.cluster_centers_  # 获取每个簇的中心点坐标 (在降维后的空间)
print("\n(3) 各簇的聚类中心 (在降维后的空间):")  # 打印任务3.3的标题
# 将聚类中心转换为DataFrame，列名为PC1, PC2...，方便查看
df_centers = pd.DataFrame(cluster_centers, columns=[f'PC{i + 1}' for i in range(Xd.shape[1])])
print(df_centers.to_string())  # 打印聚类中心

# (4) 返回各样本点到所属的聚类中心的距离平方和;
cluster_specific_inertias = []
for i in range(n_clusters):
    # 筛选出属于当前簇 i 的降维后样本点
    points_in_cluster = Xd[labels == i]
    # 获取当前簇 i 的中心点
    center_of_cluster = cluster_centers[i]
    # 计算这些点到其簇中心的距离平方和
    # (points_in_cluster - center_of_cluster) ** 2  # 对应元素平方
    # np.sum(..., axis=1)  # 对每个点的所有维度求和，得到每个点到中心的距离平方
    # np.sum(...)  # 再对簇内所有点的距离平方求和
    inertia_for_cluster = np.sum((points_in_cluster - center_of_cluster) ** 2)
    cluster_specific_inertias.append(inertia_for_cluster)
    print(f"   簇 {i}: 内部距离平方和 = {inertia_for_cluster:.4f}")

# 打印全局的 inertia，作为对比或参考
global_inertia = kmeans.inertia_
print(f"\n   全局所有样本点到其所属聚类中心的总距离平方和 (kmeans.inertia_): {global_inertia:.4f}")

# (5) 绘制降维后数据的聚类结果散点图 (包括2D和3D)
print("\n(5) 绘制降维后数据的聚类结果散点图:")

num_actual_components = Xd.shape[1]  # 获取PCA实际保留的主成分数量
fontsize_val = 12  # 定义一个基础字体大小，可以根据需要调整

# --- 首先，如果至少有2个主成分，则绘制原始的2D图 ---
if num_actual_components >= 2:
    print("   绘制 K-Means 聚类结果 (前两个主成分, PC1 vs PC2):")
    plt.figure(figsize=(10, 7))
    scatter = plt.scatter(Xd[:, 0], Xd[:, 1], c=labels, cmap='viridis', s=50, alpha=0.6)
    plt.scatter(cluster_centers[:, 0], cluster_centers[:, 1], marker='X', s=200, color='red', edgecolor='black',
                label='聚类中心')

    plt.xlabel(f'主成分 1 (PC1) - 方差贡献率: {pca.explained_variance_ratio_[0]:.2%}', fontsize=fontsize_val)
    plt.ylabel(f'主成分 2 (PC2) - 方差贡献率: {pca.explained_variance_ratio_[1]:.2%}', fontsize=fontsize_val)
    plt.title(f'K-Means聚类结果 (PC1 vs PC2, K={n_clusters})', fontsize=fontsize_val + 2)
    plt.xticks(fontsize=fontsize_val - 2)
    plt.yticks(fontsize=fontsize_val - 2)

    handles, _ = scatter.legend_elements(prop="colors", alpha=0.6)
    legend_labels_2d = [f'簇 {i}' for i in range(n_clusters)]
    center_handle = plt.Line2D([0], [0], marker='X', color='w', label='聚类中心',
                               markerfacecolor='red', markeredgecolor='black', markersize=10)

    plt.legend(handles=handles + [center_handle], labels=legend_labels_2d + ['聚类中心'],
               title="图例", fontsize=fontsize_val - 2, title_fontsize=fontsize_val, markerscale=1.0)

    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.show()

# --- 绘制一个3D图 (PC1, PC2, PC3) ---

plotted_3d_successfully = False
if num_actual_components >= 3:

    pc_indices_3d = (0, 1, 2)
    pc_names_str_3d = "PC1, PC2, PC3"
    idx1, idx2, idx3 = pc_indices_3d

    print(f"   绘制 K-Means 聚类结果 (3D: {pc_names_str_3d})")
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')

    scatter_3d = ax.scatter(Xd[:, idx1], Xd[:, idx2], Xd[:, idx3],
                            c=labels, cmap='viridis', s=30, alpha=0.4)

    ax.scatter(cluster_centers[:, idx1], cluster_centers[:, idx2], cluster_centers[:, idx3],
               marker='X', s=250, color='red', edgecolor='black', depthshade=True,
               label='聚类中心')

    ax.set_xlabel(f'主成分 {idx1 + 1} (贡献率: {pca.explained_variance_ratio_[idx1]:.2%})', fontsize=fontsize_val - 1)
    ax.set_ylabel(f'主成分 {idx2 + 1} (贡献率: {pca.explained_variance_ratio_[idx2]:.2%})', fontsize=fontsize_val - 1)
    ax.set_zlabel(f'主成分 {idx3 + 1} (贡献率: {pca.explained_variance_ratio_[idx3]:.2%})', fontsize=fontsize_val - 1)

    ax.tick_params(axis='x', labelsize=fontsize_val - 3)
    ax.tick_params(axis='y', labelsize=fontsize_val - 3)
    ax.tick_params(axis='z', labelsize=fontsize_val - 3)

    ax.set_title(f'K-Means聚类 (3D: {pc_names_str_3d}, K={n_clusters})', fontsize=fontsize_val + 1)

    handles_3d, _ = scatter_3d.legend_elements(prop="colors", alpha=0.6, num=n_clusters if n_clusters > 0 else None)
    legend_labels_3d = [f'簇 {i}' for i in range(n_clusters)]
    center_handle_3d = plt.Line2D([0], [0], marker='X', color='w', label='聚类中心',
                                  markerfacecolor='red', markeredgecolor='black', markersize=10)

    ax.legend(handles=handles_3d + [center_handle_3d], labels=legend_labels_3d + ['聚类中心'],
              title="图例", fontsize=fontsize_val - 3, title_fontsize=fontsize_val - 1, loc='upper left',
              bbox_to_anchor=(1.05, 1))

    plt.tight_layout(rect=[0, 0, 0.85, 1])
    plt.show()
    plotted_3d_successfully = True
else:
    print(f"   主成分数量 ({num_actual_components}) 少于3个，无法绘制 (PC1, PC2, PC3) 的3D聚类图。")

if plotted_3d_successfully:
    print(f"   成功绘制了 (PC1, PC2, PC3) 的3D聚类图。")

# --- 4. 数据分析(一) ---
print("\n--- 4. 数据分析(一) ---")  # 打印数据分析(一)阶段的标题
# (1) 编程获取各簇内运动学片断样本在原始数据样本的索引;
print("\n(1)各簇内样本的原始索引 (前200个示例):")  # 打印任务4.1的标题
cluster_indices = {}  # 创建一个字典来存储每个簇包含的样本在原始数据集X中的索引
for i in range(n_clusters):  # 遍历每个簇 (0到n_clusters-1)
    # np.where(labels == i) 返回一个元组，元组的第一个元素是满足条件(标签等于i)的样本的索引数组
    cluster_indices[i] = np.where(labels == i)[0]
    print(f"簇 {i} 内样本的原始索引 (前200个): {cluster_indices[i][:200]}")  # 打印索引

# (2) 打印各簇的运动学片断样本数量;
print("\n(2) 各簇的运动学片段样本数量:")

# 创建一个字典来存储每个簇的样本数量
cluster_sizes = {}  # 创建一个空字典 `cluster_indices`，用于存储每个簇所包含的样本在原始数据集X中的索引。键是簇的编号
for i in range(n_clusters):  # 遍历每个簇
    # `cluster_indices[i]` 是上一步得到的簇 `i` 内所有样本的原始索引数组。
    # `len(cluster_indices[i])` 计算这个数组的长度，即得到簇 `i` 中样本的数量。
    cluster_size = len(cluster_indices[i])
    # 将簇 `i` 的样本数量存储到 `cluster_sizes` 字典中，键为 '簇 i'。
    cluster_sizes[f'簇 {i}'] = cluster_size
    print(f"   簇 {i}: {cluster_size} 个样本")  # 打印每个簇包含的样本数量

if n_clusters > 0 and sum(cluster_sizes.values()) > 0:  # 确保聚类已经执行成功
    # 创建饼状图来可视化各簇的样本数量
    # 创建 2D 饼状图可视化各簇的样本数量分布（无立体效果）
    fig, ax = plt.subplots(figsize=(12, 8))

    sizes = list(cluster_sizes.values())  # 从 `cluster_sizes` 字典中获取所有簇的样本数量，并转换为列表。
    labels_pie = list(cluster_sizes.keys())  # 从 `cluster_sizes` 字典中获取所有簇的标签 (如 '簇 0')，并转换为列表。

    total = sum(sizes)  # 计算所有簇的总样本数量
    percentages = [size / total * 100 for size in sizes]  # 计算每个簇的样本数量占总样本数量的百分比。
    colors = plt.cm.viridis(np.linspace(0, 1, len(sizes)))  # 使用 `viridis` 颜色映射为每个簇生成一个不同的颜色。

    # 绘制平面饼图（2D）
    wedges, texts, autotexts = ax.pie(
        sizes,
        labels=labels_pie,
        autopct='%1.1f%%',
        colors=colors,
        startangle=90,
        wedgeprops={'linewidth': 1, 'edgecolor': 'white'},  # 保持清晰边界
        textprops={'fontsize': 16, 'weight': 'bold'}
    )

    # 调整文字格式
    for autotext in autotexts:
        autotext.set_fontsize(14)
        autotext.set_weight('bold')
        autotext.set_color('white')

    # 添加图标题与图例
    plt.title('各簇的运动学片段样本数量分布', fontsize=20, pad=20)

    plt.legend(
        wedges,
        [f'{label}: {size} 个样本 ({percentage:.1f}%)'
         for label, size, percentage in zip(labels_pie, sizes, percentages)],
        title="簇分布",
        loc="center left",
        bbox_to_anchor=(1, 0, 0.5, 1),
        fontsize=14,
        title_fontsize=18
    )

    plt.tight_layout()
    plt.show()

    # 另外创建一个水平条形图来展示数量对比
    plt.figure(figsize=(12, max(6, n_clusters * 1.5)))
    y_pos = np.arange(len(labels_pie))
    plt.barh(y_pos, sizes, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
    for i_bar, v_bar in enumerate(sizes):
        # 在每个条形末端添加文本标签，显示具体数量和百分比
        plt.text(v_bar + (max(sizes) * 0.01 if max(sizes) > 0 else 1), i_bar, f"{v_bar} ({percentages[i_bar]:.1f}%)",
                 va='center', fontweight='bold')
    plt.yticks(y_pos, labels_pie)
    plt.xlabel('样本数量')
    plt.title('各簇的运动学片段样本数量对比', fontsize=20)
    plt.grid(axis='x', linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.show()
else:
    print("   没有有效的簇来绘制样本数量分布图。")

# --- 5. 数据分析(二) ---
print("\n--- 5. 数据分析(二) ---")  # 打印数据分析(二)阶段的标题


# (1) 编写函数,计算簇内运动学片断样本,与其聚类中心的距离平方和,并返回各运动学片断样本的索引和距离平方和;
def get_distances_to_center(samples_in_cluster_transformed, center_coords_transformed, original_indices_in_cluster):
    """
    计算簇内每个样本到其对应聚类中心的距离的平方。

    参数:
      samples_in_cluster_transformed: np.ndarray, 包含特定簇中所有样本在降维后的主成分空间中的坐标 (例如，Xd的子集)。
                                      形状为 (n_samples_in_cluster, n_features_transformed)。
      center_coords_transformed:      np.ndarray, 表示该簇的聚类中心在降维后的主成分空间中的坐标 (来自 kmeans.cluster_centers_)。
                                      形状为 (n_features_transformed,)。
      original_indices_in_cluster:   np.ndarray, 包含这些簇内样本在原始数据集 X 中的索引。
                                      形状为 (n_samples_in_cluster,)。
    返回:
      original_indices_in_cluster (np.ndarray): 传入的原始索引数组。
      squared_distances (np.ndarray):             每个样本到其簇中心的距离平方和数组，形状为 (n_samples_in_cluster,)。
    """
    squared_distances = np.sum((samples_in_cluster_transformed - center_coords_transformed) ** 2, axis=1)
    return original_indices_in_cluster, squared_distances


# (2) 调用上述函数,计算并打印各簇内的运动学片断样本的索引和距离平方和;
print("\n(2) 各簇内样本的索引及其到聚类中心的距离平方和:")
all_cluster_sample_info = {}  # 创建一个空字典 `all_cluster_sample_info`。
# 用于存储每个簇的详细信息，包括其样本的原始索引和这些样本到各自簇中心的距离平方和。
# 键是簇编号，值是另一个包含 'indices' 和 'distances' 的字典。

for i in range(n_clusters):
    print(f"\n   --- 簇 {i} ---")
    # `cluster_indices[i]` 是簇 `i` 中所有样本的原始索引 (在之前计算得到)。
    # `Xd[cluster_indices[i]]` 从降维后的数据 `Xd` 中选取属于当前簇 `i` 的所有样本数据。
    current_cluster_samples_Xd = Xd[cluster_indices[i]]
    # `cluster_centers` 是K-Means聚类的结果，存储了每个簇的中心点坐标 (在降维空间中)。
    # `cluster_centers[i]` 获取当前簇 `i` 的中心点坐标。
    current_cluster_center_Xd = cluster_centers[i]
    # 获取当前簇 `i` 的样本在原始数据集中的索引，这在调用函数时需要。
    current_original_indices = cluster_indices[i]

    # 调用之前定义的 `get_distances_to_center` 函数。
    # 传入当前簇的降维后样本数据、簇中心坐标和原始索引。
    # 函数返回这个簇内每个样本的原始索引 (`original_indices_result`) 和它们到簇中心的距离平方和 (`sq_distances_result`)。
    original_indices_result, sq_distances_result = get_distances_to_center(
        current_cluster_samples_Xd,
        current_cluster_center_Xd,
        current_original_indices
    )

    # 将计算得到的原始索引和距离平方和存储到 `all_cluster_sample_info` 字典中，以簇编号 `i` 为键。
    all_cluster_sample_info[i] = {'indices': original_indices_result, 'distances': sq_distances_result}

    print(f"      样本的原始索引 (前10个示例): {original_indices_result[:10]}")
    print(f"      对应距离平方和 (前10个示例): {sq_distances_result[:10]}")

    # 热力图可视化
    plt.figure(figsize=(10, 8))
    n_samples_in_cluster = len(sq_distances_result)
    grid_size = int(np.ceil(np.sqrt(n_samples_in_cluster)))
    heatmap_data = np.full((grid_size, grid_size), np.nan)
    for idx, val in enumerate(sq_distances_result):
        row, col = divmod(idx, grid_size)
        heatmap_data[row, col] = val

    im = plt.imshow(heatmap_data, cmap='viridis', interpolation='nearest', aspect='auto')
    plt.colorbar(im, label='距离平方和')
    plt.title(f'簇 {i} 样本到聚类中心的距离平方和热力图')
    plt.xlabel('样本索引 (网格列)')
    plt.ylabel('样本索引 (网格行)')

    # 标记距离最小的样本在热力图中的位置
    min_idx_in_cluster = np.argmin(sq_distances_result)
    min_row, min_col = divmod(min_idx_in_cluster, grid_size)
    plt.plot(min_col, min_row, 'r*', markersize=15, label='最小距离样本')
    plt.legend()
    plt.tight_layout()
    plt.show()

# (3) 找出各簇内,与聚类中心距离平方和最小的样本,返回该样本在原始样本 X 内的索引
print("\n(3) 各簇内与聚类中心距离平方和最小的样本 (在原始样本X内的索引):")
closest_samples_original_indices = {}  # 创建一个空字典 `closest_samples_original_indices`。
# 用于存储每个簇中，距离该簇中心最近的那个样本在原始数据集 X 中的索引。
# 键是簇编号，值是该簇中最近样本的原始索引。

for i in range(n_clusters):
    # `all_cluster_sample_info[i]['distances']` 是簇 `i` 内所有样本到其簇中心的距离平方和数组。
    # `np.argmin(...)` 找到这个数组中最小值的索引。这个索引是相对于簇内样本排列的，而不是原始数据集的索引。
    idx_min_dist_relative = np.argmin(all_cluster_sample_info[i]['distances'])
    # `all_cluster_sample_info[i]['indices']` 是簇 `i` 内所有样本的原始索引数组。
    # 使用上一步得到的相对索引 `idx_min_dist_relative` 从这个原始索引数组中取出对应的原始索引。
    # 这就是簇 `i` 中距离其中心最近的样本在原始数据集 X 中的实际索引。
    original_idx_closest_sample = all_cluster_sample_info[i]['indices'][idx_min_dist_relative]
    # 获取这个最近样本的实际最小距离平方和
    min_distance_square = all_cluster_sample_info[i]['distances'][idx_min_dist_relative]
    # 将找到的最近样本的原始索引存储到 `closest_samples_original_indices` 字典中。
    closest_samples_original_indices[i] = original_idx_closest_sample
    print(f"   簇 {i}: 原始索引 {original_idx_closest_sample} (距离平方和: {min_distance_square:.4f})")

print("\n--- 所有任务执行完毕 ---")