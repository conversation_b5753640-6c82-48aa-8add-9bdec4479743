#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt # 只导入 matplotlib.pyplot
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei'
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题

# 特征名称 (根据题目表1)
feature_names = [
    "V_a (平均速度)", "V_f (平均行驶速度)", "V_max (最大速度)",
    "a_vp (平均加速度)", "a_vd (平均减速度)", "V_std (速度标准差)",
    "a_vpstd (加速度标准差)", "a_vdstd (减速度标准差)", "T (运动学片段时长)",
    "T_i (怠速时间)", "T_a (加速时间)", "T_d (减速时间)", "T_c (匀速时间)"
]

print("--- 任务开始 ---")

# --- 1. 数据预处理 ---
print("\n--- 1. 数据预处理 ---")
# (1) 将 Excel 数据文件导入 python,赋值为数组 X;
try:
    df = pd.read_excel("Data2.xlsx", header=None) # 假设没有表头行，如果Excel有表头，请删除header=None
    X = df.values
    print(f"(1) Excel数据已导入，X的形状: {X.shape}")
    if X.shape[1] != len(feature_names):
        print(f"警告: Excel列数 ({X.shape[1]}) 与特征名称数量 ({len(feature_names)}) 不匹配。请检查Excel文件或feature_names列表。")

except FileNotFoundError:
    print("(1) 错误: 未找到 'Data2.xlsx' 文件。请确保文件在当前目录下。")
    exit()
except Exception as e:
    print(f"(1) 导入Excel时发生错误: {e}")
    exit()


# (2) 编程、以表格形式打印前20个“运动学片段”样本数据;
print("\n(2) 前20个运动学片段样本数据:")
if X.shape[0] >= 20:
    # 使用 Pandas DataFrame 格式化输出
    df_preview = pd.DataFrame(X[:20, :], columns=feature_names)
    print(df_preview.to_string()) # to_string() 保证打印所有列
else:
    print("数据样本不足20个，打印所有样本：")
    df_preview = pd.DataFrame(X, columns=feature_names)
    print(df_preview.to_string())

# (3) 将样本数据 X 标准化为Xs(均值为0,标准差为1);
scaler = StandardScaler()
Xs = scaler.fit_transform(X)
print("\n(3) 样本数据已标准化为 Xs。")
print(f"Xs 的均值 (应接近0): {np.mean(Xs, axis=0)}")
print(f"Xs 的标准差 (应接近1): {np.std(Xs, axis=0)}")

# --- 2. 主成分分析 (PCA) ---
print("\n--- 2. 主成分分析 (PCA) ---")
# (1) 调用 sklearn 主成分分析接口函数,对样本数据 Xs 进行主成分分析,要求累积贡献率大于85%;
pca = PCA(n_components=0.85)
pca.fit(Xs)
print(f"(1) PCA已执行，要求累积贡献率 > 85%。")
print(f"   保留的主成分数量: {pca.n_components_}")

# (2) 打印保留的各阶主成分和对应的贡献率;
print("\n(2) 保留的各阶主成分对应的贡献率:")
explained_variance_ratios = pca.explained_variance_ratio_
for i, ratio in enumerate(explained_variance_ratios):
    print(f"   主成分 {i+1}: 贡献率 = {ratio:.4f}")
print(f"   累积贡献率: {np.sum(explained_variance_ratios):.4f}")

# (3) 分析所保留的主成分、所代表的样本数据特征;
print("\n(3) 主成分分析 (各主成分由原始特征的线性组合构成):")
print("   主成分系数 (pca.components_):")
df_pca_components = pd.DataFrame(pca.components_, columns=feature_names)
print(df_pca_components.to_string())
print("   解读提示: 上表中每一行代表一个主成分，列代表原始特征。")
print("   数值的绝对值越大，表示该原始特征对该主成分的贡献越大。")
print("   例如，如果主成分1在'V_max (最大速度)'上的系数绝对值很大，则主成分1主要反映了速度的大小。")

# (4) 绘制各阶主成分贡献率的散点图; (这里用条形图展示单个贡献率，用折线图展示累积贡献率，符合常见做法)
print("\n(4) 绘制各阶主成分贡献率的图表:")
plt.figure(figsize=(10, 6))
# 绘制单个主成分贡献率 (条形图)
plt.bar(range(1, pca.n_components_ + 1), explained_variance_ratios, alpha=0.7, align='center',
        label='单个主成分贡献率')
# 绘制累积贡献率 (折线图)
plt.plot(range(1, pca.n_components_ + 1), np.cumsum(explained_variance_ratios), marker='o', linestyle='-',
         color='red', label='累积贡献率') # 使用plot绘制折线图，可以用step或plot

# 如果题目严格要求散点图来表示“各阶主成分贡献率”，可以这样做:
# plt.scatter(range(1, pca.n_components_ + 1), explained_variance_ratios, color='blue', label='单个主成分贡献率 (散点)')
# plt.plot(range(1, pca.n_components_ + 1), np.cumsum(explained_variance_ratios), marker='o', linestyle='-',
#          color='red', label='累积贡献率')


plt.ylabel('贡献率')
plt.xlabel('主成分序号')
plt.title('主成分贡献率分析')
plt.xticks(range(1, pca.n_components_ + 1))
plt.legend(loc='best')
plt.grid(True, linestyle='--', alpha=0.7)
plt.show()

# (5) 将运动学片断的样本数据Xs,转换成降维后的数据 Xd;
Xd = pca.transform(Xs)
print("\n(5) 样本数据 Xs 已转换为降维后的数据 Xd。")
print(f"   Xd 的形状: {Xd.shape}")

# --- 3. k均值聚类分析 ---
print("\n--- 3. k均值聚类分析 ---")
# (1) 随机生成簇中心初值,设定簇数量为4,进行 k-means 聚类分析;
n_clusters = 4
kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init='auto')
kmeans.fit(Xd)
print(f"(1) k-Means聚类已执行，簇数量设置为: {n_clusters}")

# (2) 计算每个样本对应的分类;
labels = kmeans.labels_
print("\n(2) 每个样本对应的分类 (簇标签，前50个示例):")
print(labels[:50])

# (3) 给出各簇的聚类中心;
cluster_centers = kmeans.cluster_centers_
print("\n(3) 各簇的聚类中心 (在降维后的空间):")
df_centers = pd.DataFrame(cluster_centers, columns=[f'PC{i+1}' for i in range(Xd.shape[1])])
print(df_centers.to_string())

# (4) 返回各样本点到所属的聚类中心的距离平方和;
inertia = kmeans.inertia_
print(f"\n(4) 各样本点到所属聚类中心的距离平方和 (Inertia/WCSS): {inertia:.4f}")

# --- 4. 数据分析(一) ---
print("\n--- 4. 数据分析(一) ---")
# (1) 编程获取各簇内运动学片断样本在原始数据样本的索引;
cluster_indices = {}
for i in range(n_clusters):
    cluster_indices[i] = np.where(labels == i)[0]
    print(f"(1) 簇 {i} 内样本的原始索引 (前10个示例): {cluster_indices[i][:10]}")

# (2) 打印各簇的运动学片断样本数量;
print("\n(2) 各簇的运动学片段样本数量:")
for i in range(n_clusters):
    print(f"   簇 {i}: {len(cluster_indices[i])} 个样本")

# --- 5. 数据分析(二) ---
print("\n--- 5. 数据分析(二) ---")
# (1) 编写函数,计算簇内运动学片断样本,与其聚类中心的距离平方和,并返回各运动学片断样本的索引和距离平方和;
def get_distances_to_center(samples_in_cluster_transformed, center_coords_transformed, original_indices_in_cluster):
    squared_distances = np.sum((samples_in_cluster_transformed - center_coords_transformed)**2, axis=1)
    return original_indices_in_cluster, squared_distances

# (2) 调用上述函数,计算并打印各簇内的运动学片断样本的索引和距离平方和;
print("\n(2) 各簇内样本的索引及其到聚类中心的距离平方和:")
all_cluster_sample_info = {}

for i in range(n_clusters):
    print(f"\n   --- 簇 {i} ---")
    current_cluster_samples_Xd = Xd[cluster_indices[i]]
    current_cluster_center_Xd = cluster_centers[i]
    current_original_indices = cluster_indices[i]

    original_indices_result, sq_distances_result = get_distances_to_center(
        current_cluster_samples_Xd,
        current_cluster_center_Xd,
        current_original_indices
    )
    all_cluster_sample_info[i] = {'indices': original_indices_result, 'distances': sq_distances_result}

    print(f"      前5个样本的原始索引: {original_indices_result[:5]}")
    print(f"      对应距离平方和: {sq_distances_result[:5]}")


# (3) 找出各簇内,与聚类中心距离平方和最小的样本,返回该样本在原始样本 X 内的索引
print("\n(3) 各簇内与聚类中心距离平方和最小的样本 (在原始样本X内的索引):")
closest_samples_original_indices = {}
for i in range(n_clusters):
    if len(all_cluster_sample_info[i]['distances']) > 0:
        idx_min_dist_relative = np.argmin(all_cluster_sample_info[i]['distances'])
        original_idx_closest_sample = all_cluster_sample_info[i]['indices'][idx_min_dist_relative]
        min_distance_sq = all_cluster_sample_info[i]['distances'][idx_min_dist_relative]
        closest_samples_original_indices[i] = original_idx_closest_sample
        print(f"   簇 {i}: 原始索引 {original_idx_closest_sample} (距离平方和: {min_distance_sq:.4f})")
    else:
        print(f"   簇 {i}: 无样本")
        closest_samples_original_indices[i] = None


print("\n--- 所有任务执行完毕 ---")