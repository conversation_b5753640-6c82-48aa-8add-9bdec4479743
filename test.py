import pandas as pd  # 导入Pandas库，用于数据处理和Excel文件读写，通常简写为pd
import numpy as np   # 导入NumPy库，用于进行数值计算，特别是数组操作，通常简写为np
import matplotlib.pyplot as plt # 导入Matplotlib的pyplot模块，用于数据可视化（绘图），通常简写为plt
import os  # 导入os模块，用于与操作系统交互，如此处用于设置环境变量和检查文件是否存在

# 设置环境变量OMP_NUM_THREADS为1，这通常在导入scikit-learn之前进行。
# 它的目的是解决在某些情况下（特别是在KMeans等算法中）由于OpenMP多线程引起的潜在问题或警告，
# 例如内存泄漏或结果不可复现。将其设置为1可以强制使用单线程。
os.environ["OMP_NUM_THREADS"] = "1"

from sklearn.preprocessing import StandardScaler  # 从scikit-learn库中导入StandardScaler，用于特征标准化
from sklearn.decomposition import PCA  # 从scikit-learn库中导入PCA (Principal Component Analysis)，用于主成分分析（降维）
from sklearn.cluster import KMeans  # 从scikit-learn库中导入KMeans，用于进行K-均值聚类分析
from sklearn.metrics import calinski_harabasz_score

# --- Matplotlib 全局设置 ---
# 设置matplotlib支持中文显示，防止中文标签显示为方块
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定一个支持中文的字体，如'SimHei' (黑体) 或 'Microsoft YaHei' (微软雅黑)
plt.rcParams['axes.unicode_minus'] = False  # 解决坐标轴负号'-'在某些情况下显示为方块的问题

# --- 特征名称定义 ---
# 根据题目中的表1定义13个特征的名称，方便后续在DataFrame和图表中使用
feature_names = [
    "V_a (平均速度)", "V_f (平均行驶速度)", "V_max (最大速度)",
    "a_vp (平均加速度)", "a_vd (平均减速度)", "V_std (速度标准差)",
    "a_vpstd (加速度标准差)", "a_vdstd (减速度标准差)", "T (运动学片段时长)",
    "T_i (怠速时间)", "T_a (加速时间)", "T_d (减速时间)", "T_c (匀速时间)"
]

print("--- 任务开始 ---") # 打印任务开始的提示信息

# --- 1. 数据预处理 ---
print("\n--- 1. 数据预处理 ---") # 打印数据预处理阶段的标题
# (1) 将 Excel 数据文件导入 python,赋值为数组 X;
try:
    # 首先检查目标Excel文件 "Data2.xlsx" 是否存在于当前工作目录
    if os.path.exists("Data2.xlsx"):
        df = pd.read_excel("Data2.xlsx", header=0)
        X = df.values  # 将读取到的Pandas DataFrame转换为NumPy数组，赋值给X
        print(f"(1) Excel数据已导入，X的形状: {X.shape}") # 打印成功信息和X的形状 (行数, 列数)

except FileNotFoundError: # 捕获文件未找到的错误
    print("(1) 错误: 未找到 'Data2.xlsx' 文件。创建示例数据以继续演示。")


# (2) 编程、以表格形式打印前20个"运动学片段"样本数据;
print("\n(2) 前20个运动学片段样本数据:") # 打印任务2的标题
df_preview = pd.DataFrame(X[:20, :], columns=feature_names)
print(df_preview) # 这样打印出来的不是一串不便于查看
print(df_preview.to_string()) # 使用.to_string()方法打印整个DataFrame，确保所有列都被显示


# (3) 将样本数据 X 标准化为Xs(均值为0,标准差为1);
scaler = StandardScaler() # 创建StandardScaler对象
Xs = scaler.fit_transform(X) # 对数据X进行拟合（计算均值和标准差）并转换（标准化处理）
print("\n(3) 样本数据已标准化为 Xs。") # 打印标准化完成的提示
print(f"Xs 的均值 (应接近0): {np.mean(Xs, axis=0)}") # 打印标准化后数据各列的均值，应非常接近0
print(f"Xs 的标准差 (应接近1): {np.std(Xs, axis=0)}") # 打印标准化后数据各列的标准差，应非常接近1



# --- 2. 主成分分析 (PCA) ---
print("\n--- 2. 主成分分析 (PCA) ---") # 打印PCA阶段的标题
# (1) 调用 sklearn 主成分分析接口函数,对样本数据 Xs 进行主成分分析,要求累积贡献率大于85%;
# n_components=0.85 表示希望保留的主成分能够解释至少85%的原始数据方差
pca = PCA(n_components=0.85)
pca.fit(Xs) # 对标准化后的数据Xs进行PCA拟合
print(f"(1) PCA已执行，要求累积贡献率 > 85%。") # 打印PCA执行完成的提示
print(f"   保留的主成分数量: {pca.n_components_}") # 打印实际保留的主成分数量

# (2) 打印保留的各阶主成分和对应的贡献率;
print("\n(2) 保留的各阶主成分对应的贡献率:") # 打印任务2.2的标题
explained_variance_ratios = pca.explained_variance_ratio_ # 获取每个保留主成分的方差解释比例（贡献率）explained_variance_ratio_是pca对象的属性
for i, ratio in enumerate(explained_variance_ratios): # 遍历每个主成分的贡献率
    print(f"   主成分 {i+1}: 贡献率 = {ratio:.4f}") # 打印主成分序号和其贡献率（保留4位 小数）
print(f"   累积贡献率: {np.sum(explained_variance_ratios):.4f}") # 打印所有保留主成分的累积贡献率

# (3) 分析所保留的主成分、所代表的样本数据特征;
print("\n(3) 主成分分析 (各主成分由原始特征的线性组合构成):") # 打印任务2.3的标题
print("   主成分系数 (pca.components_):") # pca.components_ 存储了主成分向量
# 将主成分系数（每个主成分由原始特征线性组合的权重）转换为DataFrame，方便查看
df_pca_components = pd.DataFrame(pca.components_, columns=feature_names)#colums是表头
print(df_pca_components.to_string()) # 打印主成分系数矩阵
print("   题注: 上表中每一行代表一个主成分，列代表原始特征。")
print("   数值的绝对值越大，表示该原始特征对该主成分的贡献越大。")
# 这里的分析通常需要人工解读pca.components_的内容，理解每个主成分主要由哪些原始特征构成。

# (4) 绘制各阶主成分贡献率的散点图; (根据要求，这里明确使用散点图展示单个主成分贡献率)
print("\n(4) 绘制各阶主成分贡献率的图表:") # 打印任务2.4的标题
plt.figure(figsize=(10, 6)) # 创建一个新的图形，并设置其大小

# 绘制单个主成分贡献率 (使用散点图)
# x轴是主成分序号 (从1开始)，y轴是对应的贡献率
# s=100 设置散点的大小，alpha=0.7 设置透明度
plt.scatter(range(1, pca.n_components_ + 1), explained_variance_ratios, color='blue', s=100, alpha=0.7, label='单个主成分贡献率')

# 绘制累积贡献率 (使用折线图)
# x轴是主成分序号，y轴是累积贡献率
# marker='o' 在每个数据点处显示一个圆圈标记，linestyle='-' 使用实线连接各点
plt.plot(range(1, pca.n_components_ + 1), np.cumsum(explained_variance_ratios), marker='o', linestyle='-', color='red', label='累积贡献率')
plt.ylabel('贡献率') # 设置y轴标签
plt.xlabel('主成分序号') # 设置x轴标签
plt.title('主成分贡献率分析') # 设置图表标题
plt.legend(loc='best') # 显示图例，并自动选择最佳位置
plt.grid(True, linestyle='--', alpha=0.7) # 显示网格线，使用虚线，并设置透明度
plt.show() # 显示图表

# (5) 将运动学片断的样本数据Xs,转换成降维后的数据 Xd;
Xd = pca.transform(Xs) # 使用拟合好的PCA模型将标准化数据Xs转换（投影）到新的主成分空间，得到降维后的数据Xd
print("\n(5) 样本数据 Xs 已转换为降维后的数据 Xd。") # 打印转换完成的提示
print(f"   Xd 的形状: {Xd.shape}") # 打印降维后数据Xd的形状，列数应等于保留的主成分数量




# --- 3. k均值聚类分析 ---
print("\n--- 3. k均值聚类分析 ---") # 打印K-均值聚类阶段的标题
# (1) 随机生成簇中心初值,设定簇数量为4,进行 k-means 聚类分析;
n_clusters = 4 # 设定聚类的簇数量为4
# 为了处理不同sklearn版本KMeans的n_init参数差异（旧版只接受整数，新版可以是'auto'）
try:
    # 尝试使用 n_init='auto'，这是较新版本的推荐设置，它会根据数据自动选择运行次数。
    # random_state=42 确保KMeans的初始中心点选择是可复现的，便于调试和比较结果。
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init='auto')
except TypeError: # 如果因为n_init='auto'导致TypeError（通常发生在旧版本sklearn）
    # 回退到使用一个固定的整数作为n_init的值，例如10。
    # n_init=10 表示KMeans算法会用10组不同的初始中心点运行，并选择结果最好的那一组。
    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
kmeans.fit(Xd) # 对降维后的数据Xd进行K-均值聚类拟合
print(f"(1) k-Means聚类已执行，簇数量为: {n_clusters}") # 打印聚类完成的提示
labels = kmeans.labels_ # 获取每个样本被分配到的簇标签 (0到n_clusters-1)
ch_score = calinski_harabasz_score(Xd, labels)
print(f"    Calinski-Harabasz (CH) 分数: {ch_score:.4f}")

# (2) 计算每个样本对应的分类;
print("\n(2) 每个样本对应的分类:")
print(labels[:200])


# (3) 给出各簇的聚类中心;
cluster_centers = kmeans.cluster_centers_ # 获取每个簇的中心点坐标 (在降维后的空间)
print("\n(3) 各簇的聚类中心 (在降维后的空间):") # 打印任务3.3的标题
#print(cluster_centers)
# 将聚类中心转换为DataFrame，列名为PC1, PC2，方便查看
df_centers = pd.DataFrame(cluster_centers, columns=[f'PC{i+1}' for i in range(Xd.shape[1])])
print(df_centers.to_string()) # 打印聚类中心


# (4) 返回各样本点到所属的聚类中心的距离平方和;
inertia = kmeans.inertia_ # 获取所有样本点到其所属簇中心的距离平方之和 (也称为WCSS - Within-Cluster Sum of Squares)
print(f"\n(4) 各样本点到所属聚类中心的距离平方和: {inertia:.4f}") # 打印Inertia值





# --- 4. 数据分析(一) ---
print("\n--- 4. 数据分析(一) ---") # 打印数据分析(一)阶段的标题
# (1) 编程获取各簇内运动学片断样本在原始数据样本的索引;
print("\n(1)各簇内样本的原始索引：") # 打印任务4.1的标题
cluster_indices = {} # 创建一个字典来存储每个簇包含的样本在原始数据集X中的索引
for i in range(n_clusters): # 遍历每个簇 (0到n_clusters-1)
    # np.where(labels == i) 返回一个元组，元组的第一个元素是满足条件(标签等于i)的样本的索引数组
    cluster_indices[i] = np.where(labels == i)[0]
    print(f"簇 {i} 内样本的原始索引: {cluster_indices[i][:200]}") # 打印索引

# (2) 打印各簇的运动学片断样本数量;
print("\n(2) 各簇的运动学片段样本数量:")
for i in range(n_clusters): # 遍历每个簇
    print(f"   簇 {i}: {len(cluster_indices[i])} 个样本") # 打印每个簇包含的样本数量





# --- 5. 数据分析(二) ---
print("\n--- 5. 数据分析(二) ---") # 打印数据分析(二)阶段的标题
# (1) 编写函数,计算簇内运动学片断样本,与其聚类中心的距离平方和,并返回各运动学片断样本的索引和距离平方和;
def get_distances_to_center(samples_in_cluster_transformed, center_coords_transformed, original_indices_in_cluster):
    """
    samples_in_cluster_transformed应该是一个数组，表示降维后的簇内样本数据。
    center_coords_transformed是簇中心的坐标，同样是降维后的坐标。
    original_indices_in_cluster是这些样本在原始数据中的索引。
    """
    # 计算每个样本与中心点在每个维度上的差值，然后平方，再按样本（axis=1）求和
    squared_distances = np.sum((samples_in_cluster_transformed - center_coords_transformed)**2, axis=1)
    return original_indices_in_cluster, squared_distances # 返回原始索引和对应的距离平方和

# (2) 调用上述函数,计算并打印各簇内的运动学片断样本的索引和距离平方和;
print("\n(2) 各簇内样本的索引及其到聚类中心的距离平方和:") # 打印任务5.2的标题
all_cluster_sample_info = {} # 创建一个字典来存储每个簇中样本的详细信息（索引和距离）

for i in range(n_clusters): # 遍历每个簇
    print(f"\n   --- 簇 {i} ---") # 打印当前处理的簇的标记
    # 获取当前簇的所有样本（降维后的数据Xd）
    current_cluster_samples_Xd = Xd[cluster_indices[i]]
    # 获取当前簇的聚类中心（降维后的坐标）
    current_cluster_center_Xd = cluster_centers[i]
    # 获取当前簇样本在原始数据集X中的索引
    current_original_indices = cluster_indices[i]

    # 调用之前定义的函数计算距离
    original_indices_result, sq_distances_result = get_distances_to_center(
        current_cluster_samples_Xd,
        current_cluster_center_Xd,
        current_original_indices
    )
    # 将结果存储到all_cluster_sample_info字典中
    all_cluster_sample_info[i] = {'indices': original_indices_result, 'distances': sq_distances_result}

    # 打印该簇前5个样本的原始索引和对应的距离平方和作为示例
    print(f"      样本的原始索引:\n {original_indices_result[:]}")
    print(f"      对应距离平方和:\n {sq_distances_result[:]}")

    # 为当前簇创建热力图可视化
    plt.figure(figsize=(10, 8))

    # 准备热力图数据 - 将距离平方和重塑为2D网格
    n_samples = len(sq_distances_result)
    grid_size = int(np.ceil(np.sqrt(n_samples)))  # 计算需要的网格大小，使其能容纳所有样本

    # 创建一个填充了NaN的网格，以便处理非完美平方数的情况
    heatmap_data = np.full((grid_size, grid_size), np.nan)

    # 填充实际数据
    for idx, val in enumerate(sq_distances_result):
        if idx < grid_size * grid_size:
            row = idx // grid_size
            col = idx % grid_size
            heatmap_data[row, col] = val

    # 绘制热力图
    im = plt.imshow(heatmap_data, cmap='viridis', interpolation='nearest')
    plt.colorbar(im, label='距离平方和')
    plt.title(f'簇 {i} 样本到聚类中心的距离平方和热力图')
    plt.xlabel('样本索引 (网格列)')
    plt.ylabel('样本索引 (网格行)')

    # 添加最小值标记
    min_idx = np.argmin(sq_distances_result)
    min_row = min_idx // grid_size
    min_col = min_idx % grid_size
    plt.plot(min_col, min_row, 'r*', markersize=15, label='最小距离样本')
    plt.legend()

    plt.tight_layout()
    plt.show()


# (3) 找出各簇内,与聚类中心距离平方和最小的样本,返回该样本在原始样本 X 内的索引
print("\n(3) 各簇内与聚类中心距离平方和最小的样本 (在原始样本X内的索引):") # 打印任务5.3的标题
closest_samples_original_indices = {} # 创建一个字典来存储每个簇中离中心最近的样本的原始索引
for i in range(n_clusters): # 遍历每个簇
    if len(all_cluster_sample_info[i]['distances']) > 0: # 确保当前簇中有样本
        # np.argmin() 返回数组中最小值的索引 (这是相对于 all_cluster_sample_info[i]['distances'] 数组的相对索引)
        idx_min_dist_relative = np.argmin(all_cluster_sample_info[i]['distances'])
        # 使用这个相对索引从原始索引数组中找到该样本在原始数据集X中的绝对索引
        original_idx_closest_sample = all_cluster_sample_info[i]['indices'][idx_min_dist_relative]
        # 获取最小的距离平方和值
        min_distance_sq = all_cluster_sample_info[i]['distances'][idx_min_dist_relative]
        # 将最近样本的原始索引存储起来
        closest_samples_original_indices[i] = original_idx_closest_sample
        print(f"   簇 {i}: 原始索引 {original_idx_closest_sample} (距离平方和: {min_distance_sq:.4f})")
    else: # 如果簇中没有样本
        print(f"   簇 {i}: 无样本")
        closest_samples_original_indices[i] = None # 标记为None


print("\n--- 所有任务执行完毕 ---") # 打印所有任务执行完毕的提示信息