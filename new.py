###############################################################################
#
# Dataset.
#
###############################################################################
import os
import random
import codecs  # For decoding the Fashion-MNIST data.
import numpy as np
from tqdm import tqdm  # Show your progress with a progress bar.
import torch


def set_seed(seed=1234):
    """
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


set_seed()


class FashionMNIST_Dataset(torch.utils.data.Dataset):
    """
    """

    def __init__(self, root, is_train):
        """
        Args:
          root      (str): The root path of the dataset.
          is_train (bool): Is the dataset used for training?

        """
        super(torch.utils.data.Dataset, self).__init__()
        self.root = root
        self.is_train = is_train
        self.images, self.labels = self._prepare_data()

    def __getitem__(self, index):
        """
        Args:
          index (int): Index

        Returns:
          image (torch.Tensor, shape [1, 28, 28]): Hand writting image.
          label (int): The class id of the image.
        """
        image = self.images[index]
        label = self.labels[index]

        #
        # Here you can perform some pre-processing.
        #

        # Insert a new dim.
        image = image[np.newaxis, :, :]
        image = image.float()

        # ...

        return image, label

    def __len__(self):
        """
        """
        return len(self.images)

    def _prepare_data(self):
        """

        Args:

        Returns:
          images
          labels
        """
        image_file_name = "{}-images-idx3-ubyte".format(
            'train' if self.is_train else 't10k'
        )
        label_file_name = "{}-labels-idx1-ubyte".format(
            'train' if self.is_train else 't10k'
        )
        image_path = os.path.join(self.root, image_file_name)
        label_path = os.path.join(self.root, label_file_name)

        # Load & parsing images.
        images = self._read_sn3_pascalvincent_tensor(image_path, strict=False)
        assert (images.dtype == torch.uint8)
        assert (images.ndimension() == 3)

        # Load & parsing labels.
        labels = self._read_sn3_pascalvincent_tensor(label_path, strict=False)
        assert (labels.dtype == torch.uint8)
        assert (labels.ndimension() == 1)
        labels = labels.long()

        return images, labels

    def _read_sn3_pascalvincent_tensor(self, path: str, strict: bool = True) -> torch.Tensor:
        """Read a SN3 file in "Pascal Vincent" format (Lush file 'libidx/idx-io.lsh').
           Argument may be a filename, compressed filename, or file object.
           This is the official code of PyTorch
        """

        SN3_PASCALVINCENT_TYPEMAP = {
            8: (torch.uint8, np.uint8, np.uint8),
            9: (torch.int8, np.int8, np.int8),
            11: (torch.int16, np.dtype('>i2'), 'i2'),
            12: (torch.int32, np.dtype('>i4'), 'i4'),
            13: (torch.float32, np.dtype('>f4'), 'f4'),
            14: (torch.float64, np.dtype('>f8'), 'f8')
        }

        def get_int(b: bytes) -> int:
            return int(codecs.encode(b, 'hex'), 16)

        # read
        with open(path, "rb") as f:
            data = f.read()
        # parse
        magic = get_int(data[0:4])
        nd = magic % 256
        ty = magic // 256
        assert 1 <= nd <= 3
        assert 8 <= ty <= 14
        m = SN3_PASCALVINCENT_TYPEMAP[ty]
        s = [get_int(data[4 * (i + 1): 4 * (i + 2)]) for i in range(nd)]
        parsed = np.frombuffer(data, dtype=m[1], offset=(4 * (nd + 1)))
        assert parsed.shape[0] == np.prod(s) or not strict
        return torch.from_numpy(parsed.astype(m[2], copy=False)).view(*s)


def unit_test1():
    """ Unit test of the MNIST_Dataset
    """
    from matplotlib import pyplot as plt
    dataset = FashionMNIST_Dataset('./data', True)
    print(len(dataset))
    print(dataset.images.shape)
    print(dataset.labels.shape)

    # The returned results are determined by '__getitem__()'
    plt.subplot(191)
    for idx, (image, label) in enumerate(dataset):
        # print(image.shape, label)
        plt.subplot(1, 9, idx + 1)
        plt.imshow(image[0])

        if idx >= 8:
            break

