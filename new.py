###############################################################################
#
# Network.
#
###############################################################################
###############################################################################
#
# Dataset.
#
###############################################################################
import os
import random
import codecs  # For decoding the Fashion-MNIST data.
import numpy as np
from tqdm import tqdm  # Show your progress with a progress bar.
import torch


def set_seed(seed=1234):
    """
    """
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


set_seed()


class FashionMNIST_Dataset(torch.utils.data.Dataset):
    """
    """

    def __init__(self, root, is_train):
        """
        Args:
          root      (str): The root path of the dataset.
          is_train (bool): Is the dataset used for training?

        """
        super(torch.utils.data.Dataset, self).__init__()
        self.root = root
        self.is_train = is_train
        self.images, self.labels = self._prepare_data()

    def __getitem__(self, index):
        """
        Args:
          index (int): Index

        Returns:
          image (torch.Tensor, shape [1, 28, 28]): Hand writting image.
          label (int): The class id of the image.
        """
        image = self.images[index]
        label = self.labels[index]

        #
        # Here you can perform some pre-processing.
        #

        # Insert a new dim.
        image = image[np.newaxis, :, :]
        image = image.float()

        # ...

        return image, label

    def __len__(self):
        """
        """
        return len(self.images)

    def _prepare_data(self):
        """

        Args:

        Returns:
          images
          labels
        """
        image_file_name = "{}-images-idx3-ubyte".format(
            'train' if self.is_train else 't10k'
        )
        label_file_name = "{}-labels-idx1-ubyte".format(
            'train' if self.is_train else 't10k'
        )
        image_path = os.path.join(self.root, image_file_name)
        label_path = os.path.join(self.root, label_file_name)

        # Load & parsing images.
        images = self._read_sn3_pascalvincent_tensor(image_path, strict=False)
        assert (images.dtype == torch.uint8)
        assert (images.ndimension() == 3)

        # Load & parsing labels.
        labels = self._read_sn3_pascalvincent_tensor(label_path, strict=False)
        assert (labels.dtype == torch.uint8)
        assert (labels.ndimension() == 1)
        labels = labels.long()

        return images, labels

    def _read_sn3_pascalvincent_tensor(self, path: str, strict: bool = True) -> torch.Tensor:
        """Read a SN3 file in "Pascal Vincent" format (Lush file 'libidx/idx-io.lsh').
           Argument may be a filename, compressed filename, or file object.
           This is the official code of PyTorch
        """

        SN3_PASCALVINCENT_TYPEMAP = {
            8: (torch.uint8, np.uint8, np.uint8),
            9: (torch.int8, np.int8, np.int8),
            11: (torch.int16, np.dtype('>i2'), 'i2'),
            12: (torch.int32, np.dtype('>i4'), 'i4'),
            13: (torch.float32, np.dtype('>f4'), 'f4'),
            14: (torch.float64, np.dtype('>f8'), 'f8')
        }

        def get_int(b: bytes) -> int:
            return int(codecs.encode(b, 'hex'), 16)

        # read
        with open(path, "rb") as f:
            data = f.read()
        # parse
        magic = get_int(data[0:4])
        nd = magic % 256
        ty = magic // 256
        assert 1 <= nd <= 3
        assert 8 <= ty <= 14
        m = SN3_PASCALVINCENT_TYPEMAP[ty]
        s = [get_int(data[4 * (i + 1): 4 * (i + 2)]) for i in range(nd)]
        parsed = np.frombuffer(data, dtype=m[1], offset=(4 * (nd + 1)))
        assert parsed.shape[0] == np.prod(s) or not strict
        return torch.from_numpy(parsed.astype(m[2], copy=True)).view(*s)  # 这里是false时会有warning,尝试修改为True


def unit_test1():
    """ Unit test of the MNIST_Dataset
    """
    from matplotlib import pyplot as plt
    dataset = FashionMNIST_Dataset('./data', True)
    print(len(dataset))
    print(dataset.images.shape)
    print(dataset.labels.shape)

    # The returned results are determined by '__getitem__()'
    plt.subplot(191)
    for idx, (image, label) in enumerate(dataset):
        # print(image.shape, label)
        plt.subplot(1, 9, idx + 1)
        plt.imshow(image[0])

        if idx >= 8:
            break


class ModifiedLeNet5(torch.nn.Module):
    """
    """

    def __init__(self):
        """ [TODO] Complete it.
        """
        super(ModifiedLeNet5, self).__init__()

        self.c1 = torch.nn.Conv2d(in_channels=1, out_channels=6,
                                  kernel_size=5, stride=1,
                                  padding=2, bias=True)

        self.s1 = torch.nn.MaxPool2d(kernel_size=2, stride=2)  # [TODO] Complete this.
        self.a1 = torch.nn.ReLU()  # [TODO] Complete this.

        self.c2 = torch.nn.Conv2d(in_channels=6, out_channels=16, kernel_size=5, stride=1, padding=0,
                                  bias=True)  # [TODO] Complete this.
        self.s2 = torch.nn.MaxPool2d(kernel_size=2, stride=2)  # [TODO] Complete this.
        self.a2 = torch.nn.ReLU()  # [TODO] Complete this.

        self.c3 = torch.nn.Conv2d(in_channels=16, out_channels=120, kernel_size=3, stride=1, padding=0,
                                  bias=True)  # [TODO] Complete this.
        self.a3 = torch.nn.ReLU()  # [TODO] Complete this.

        self.f1 = torch.nn.Linear(in_features=120 * 3 * 3, out_features=84, bias=True)  # [TODO] Complete this.
        self.a4 = torch.nn.ReLU()  # [TODO] Complete this.

        self.out = torch.nn.Linear(84, 10, bias=True)

    def forward(self, input_x):
        """ [TODO] Complete it.
        """
        x = self.c1(input_x)

        # [TODO] Write your code here...
        # C1 -> S1 -> A1
        x = self.c1(input_x)
        x_after_c1 = x.clone()  # 保存 c1 输出用于分析

        x = self.s1(x)
        x = self.a1(x)

        # C2 -> S2 -> A2
        x = self.c2(x)
        x_after_s2_path = self.s2(x)  # 保存 s2 输出用于分析 (在 a2 之前)
        x = self.a2(x_after_s2_path.clone())  # 注意这里传入的是 s2 的输出

        # C3 -> A3
        x = self.c3(x)
        x_after_a3 = self.a3(x.clone())  # 保存 a3 输出用于分析

        # Flatten 操作
        x = x_after_a3.view(x_after_a3.size(0), -1)

        # F1 -> A4
        x_after_f1 = self.f1(x.clone())  # 保存 f1 输出用于分析
        x = self.a4(x_after_f1.clone())

        x_final = self.out(x)  # 最终输出

        # 返回最终输出以及用于分析的中间层输出
        return x_final, x_after_c1, x_after_s2_path, x_after_a3, x_after_f1


def analyze_layer_outputs():
    model = ModifiedLeNet5()

    shape_input = torch.randn(4, 1, 28, 28)
    print(f"输入形状: {shape_input.shape}\n")

    _final_output, out_c1, out_s2, out_a3, out_f1 = model(shape_input)

    print(out_c1.shape)  # c1 输出形状
    print(out_s2.shape)  # s2 输出形状
    print(out_a3.shape)  # a3 输出形状
    print(out_f1.shape)  # f1 输出形状
    print("结束\n")


if __name__ == '__main__':
    unit_test1()
    analyze_layer_outputs()

###########################################################
#
# Codes for training.
#
###########################################################

# Step1: Data processing.
train_dataset = FashionMNIST_Dataset('./data',
                                     is_train=True)
train_loader = torch.utils.data.DataLoader(
    train_dataset,
    batch_size=64,  # [TODO] Complete this.
    shuffle=True,
    num_workers=0
)

# Step2: Choose the model.
model = ModifiedLeNet5()  # [TODO] Complete this.

# Step3: Choose the loss function.
loss_func = torch.nn.CrossEntropyLoss()  # [TODO] Complete this.

# Step4: For optimizing parameters of the model.
optimizer = torch.optim.SGD(
    model.parameters(),  # 传入模型的所有可训练参数
    lr=0.01,  # 初始学习率
    weight_decay=0.0005  # 正则化权重
)  # [TODO] Complete this.完成优化器的实例化，优化器选择SGD优化器，初始学习率设为0.01，正则化权重设为0.0005。

# Create the LR policy. Decrease the lr by 0.5x per epoch.
lr_scheduler = torch.optim.lr_scheduler.StepLR(optimizer,
                                               step_size=1,
                                               gamma=0.5)

# Start training.
model.train()
model.float()
for epoch in range(5):
    epoch_loss = 0.0  # 用于累计每个epoch的总损失
    num_batches = 0  # 用于计算每个epoch的批次数
    for batch_index, (images, labels) in enumerate(tqdm(train_loader, desc=f"Epoch {epoch + 1}/5")):

        outputs = model(images)[0]  # [TODO] Complete this.
        # 2. Calc. the loss.
        loss = loss_func(outputs, labels)  # [TODO] Complete this.

        # 3. Back propagation. [TODO] Complete this.
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
        epoch_loss += loss.item()
        num_batches += 1

        if batch_index % 50 == 0:
            print("[Epoch {}  Step {}] [{} in 60000] [LR: {:.4f}] [Loss: {:.5f}]".format(
                epoch, batch_index,
                min((batch_index + 1) * 128, 60000),
                optimizer.param_groups[0]['lr'],
                loss.item()
            ))

    # 4. Update the learning rate.
    lr_scheduler.step()
    print(
        f"--- Epoch {epoch + 1} 完成 --- 平均损失: {epoch_loss / num_batches:.5f} --- 新学习率: {optimizer.param_groups[0]['lr']:.4f} ---")

print("--- 训练完成 ---")

##############################################################
#
# Codes for testing.
#
##############################################################

# 【功能完善】定义测试数据集和数据加载器
test_dataset = FashionMNIST_Dataset('./data', is_train=False)  # 测试数据集，is_train=False
test_loader = torch.utils.data.DataLoader(
    test_dataset,
    batch_size=100,  # 【功能完善】批次大小设置为100
    shuffle=False,  # 【功能完善】不随机打乱
    num_workers=0
)

print(f"测试数据集大小: {len(test_dataset)}")
print(f"测试批次数量: {len(test_loader)}")
print("-" * 60)

# Start evaluation(testing).
model.eval()  # 设置模型为评估模式
model.float()

with torch.no_grad():  # 测试时不需要计算梯度
    tp = 0  # 正确预测的总数
    total_samples = 0  # 总样本数

    for batch_idx, (images, labels) in enumerate(tqdm(test_loader, desc="Testing")):
        # 获取模型输出
        outputs = model(images)  # 模型前向传播

        # 【功能完善】获取预测标签
        pred_labels = torch.argmax(outputs, dim=1)  # 取概率最大的类别作为预测标签

        # 【功能完善】计算正确预测数量
        tp += torch.sum(pred_labels == labels)  # 累加正确预测的数量
        total_samples += labels.size(0)  # 累加总样本数

        # 每10个批次打印一次进度
        if batch_idx % 10 == 0:
            current_acc = tp.item() / total_samples * 100
            print(f"批次 {batch_idx}/{len(test_loader)} - 当前准确率: {current_acc:.2f}%")

    # 计算最终准确率
    final_accuracy = tp.item() / total_samples * 100
    print("-" * 60)
    print(f"测试完成!")
    print(f"总样本数: {total_samples}")
    print(f"正确预测数: {tp.item()}")
    print(f"最终准确率: {final_accuracy:.4f}%")


# 额外的详细评估指标
def detailed_evaluation():
    """计算更详细的评估指标"""
    model.eval()

    all_predictions = []
    all_labels = []

    with torch.no_grad():
        for images, labels in test_loader:
            outputs = model(images)
            pred_labels = torch.argmax(outputs, dim=1)

            all_predictions.extend(pred_labels.cpu().numpy())
            all_labels.extend(labels.cpu().numpy())

    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)

    # 计算每个类别的准确率
    class_names = ['T-shirt/top', 'Trouser', 'Pullover', 'Dress', 'Coat',
                   'Sandal', 'Shirt', 'Sneaker', 'Bag', 'Ankle boot']

    print("\n=== 各类别详细准确率 ===")
    for i in range(10):
        class_mask = (all_labels == i)
        if np.sum(class_mask) > 0:
            class_acc = np.sum((all_predictions == all_labels) & class_mask) / np.sum(class_mask)
            print(f"{class_names[i]}: {class_acc:.4f} ({class_acc * 100:.2f}%)")

    # 计算混淆矩阵的一些统计信息
    from collections import Counter

    print("\n=== 预测分布统计 ===")
    pred_counter = Counter(all_predictions)
    true_counter = Counter(all_labels)

    for i in range(10):
        print(f"{class_names[i]}: 真实数量={true_counter[i]}, 预测数量={pred_counter[i]}")


# 运行详细评估
print("\n" + "=" * 60)
detailed_evaluation()